import { googleDriveAPI } from "../api/google-drive-api.js";
import { supabaseClient } from "../database/supabase.js";

/**
 * Drive Scanner Service
 * Comprehensive scanning service for Google Drive with scope selection
 */
export class DriveScanner {
  constructor() {
    this.driveAPI = googleDriveAPI;
    this.supabase = supabaseClient;

    // Scanning configuration
    this.config = {
      maxDepth: 100,
      batchSize: 100,
      maxConcurrentRequests: 5,
      supportedMimeTypes: [
        "application/vnd.google-apps.document",
        "application/vnd.google-apps.spreadsheet",
        "application/vnd.google-apps.presentation",
        "application/vnd.google-apps.folder",
        "application/pdf",
        "image/jpeg",
        "image/png",
        "text/plain",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ],
    };

    // Scanning state
    this.scanState = {
      isScanning: false,
      totalFiles: 0,
      scannedFiles: 0,
      currentDepth: 0,
      errors: [],
      startTime: null,
      estimatedTimeRemaining: null,
    };

    // Path cache for folder resolution
    this.pathCache = new Map();
    this.folderHierarchy = new Map();
  }

  /**
   * Start comprehensive Drive scan
   * @param {string} userEmail - User email
   * @param {object} options - Scan options
   * @returns {Promise<object>} Scan results
   */
  async startFullScan(userEmail, options = {}) {
    try {
      console.log(`🔍 Starting full Drive scan for ${userEmail}`);

      this.scanState.isScanning = true;
      this.scanState.startTime = Date.now();
      this.scanState.errors = [];

      const scanOptions = {
        includeSharedDrives: true,
        includeTrash: false,
        maxDepth: options.maxDepth || this.config.maxDepth,
        filterMimeTypes:
          options.filterMimeTypes || this.config.supportedMimeTypes,
        ...options,
      };

      // Create scan session in database
      const scanSession = await this.createScanSession(userEmail, scanOptions);

      // Start scanning from root
      const results = await this.scanFromRoot(
        userEmail,
        scanOptions,
        scanSession.id
      );

      // Update scan session with results
      await this.updateScanSession(scanSession.id, {
        status: "completed",
        total_files: results.totalFiles,
        total_size: results.totalSize,
        scan_duration: Date.now() - this.scanState.startTime,
        completed_at: new Date().toISOString(),
      });

      this.scanState.isScanning = false;

      console.log(`✅ Full scan completed: ${results.totalFiles} files found`);
      return {
        sessionId: scanSession.id,
        ...results,
      };
    } catch (error) {
      this.scanState.isScanning = false;
      console.error("❌ Error during full scan:", error.message);
      throw new Error(`Drive scan failed: ${error.message}`);
    }
  }

  /**
   * Scan from root directory
   * @param {string} userEmail - User email
   * @param {object} options - Scan options
   * @param {string} sessionId - Scan session ID
   * @returns {Promise<object>} Scan results
   */
  async scanFromRoot(userEmail, options, sessionId) {
    const allFiles = [];
    const folderQueue = [{ id: "root", path: "/", depth: 0 }];
    const processedFolders = new Set();

    while (folderQueue.length > 0) {
      const currentFolder = folderQueue.shift();

      if (
        processedFolders.has(currentFolder.id) ||
        currentFolder.depth >= options.maxDepth
      ) {
        continue;
      }

      processedFolders.add(currentFolder.id);
      this.scanState.currentDepth = Math.max(
        this.scanState.currentDepth,
        currentFolder.depth
      );

      try {
        // Get files in current folder
        const folderFiles = await this.scanFolder(
          userEmail,
          currentFolder,
          options
        );

        // Process each file
        const existingFileIds =
          (
            await this.supabase
              .getServiceClient()
              .from("scanned_files")
              .select("file_id")
              .in(
                "file_id",
                folderFiles.map((f) => f.id)
              )
          ).data?.map((row) => row.file_id) || [];
        const newFiles = folderFiles.filter(
          (file) => !existingFileIds.includes(file.id)
        );

        for (const file of newFiles) {
          // Add path information
          file.fullPath = this.buildFilePath(currentFolder.path, file.name);
          file.depth = currentFolder.depth;
          file.scanSessionId = sessionId;

          // Store file in database
          await this.storeScannedFile(file, sessionId);

          allFiles.push(file);

          // If it's a folder, add to queue for scanning
          if (file.mimeType === "application/vnd.google-apps.folder") {
            folderQueue.push({
              id: file.id,
              path: file.fullPath,
              depth: currentFolder.depth + 1,
            });
          }
        }

        this.scanState.scannedFiles += newFiles.length;

        // Update progress
        await this.updateScanProgress(sessionId, {
          scanned_files: this.scanState.scannedFiles,
          current_depth: this.scanState.currentDepth,
          folders_processed: processedFolders.size,
        });

        console.log(
          `📁 Scanned folder: ${currentFolder.path} (${newFiles.length} files, depth: ${currentFolder.depth})`
        );
      } catch (error) {
        console.error(
          `❌ Error scanning folder ${currentFolder.path}:`,
          error.message
        );
        this.scanState.errors.push({
          folder: currentFolder.path,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }

      // Small delay to respect rate limits
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    return {
      totalFiles: allFiles.length,
      totalSize: allFiles.reduce(
        (sum, file) => sum + (parseInt(file.size) || 0),
        0
      ),
      folders: allFiles.filter(
        (f) => f.mimeType === "application/vnd.google-apps.folder"
      ).length,
      documents: allFiles.filter((f) => f.mimeType.includes("google-apps"))
        .length,
      maxDepth: this.scanState.currentDepth,
      errors: this.scanState.errors,
      files: allFiles,
    };
  }

  /**
   * Scan specific folder
   * @param {string} userEmail - User email
   * @param {object} folder - Folder info
   * @param {object} options - Scan options
   * @returns {Promise<Array>} Files in folder
   */
  async scanFolder(userEmail, folder, options) {
    const query =
      folder.id === "root"
        ? `'${folder.id}' in parents and trashed=false`
        : `'${folder.id}' in parents and trashed=false`;

    const scanOptions = {
      q: query,
      fields:
        "nextPageToken, files(id, name, mimeType, size, parents, createdTime, modifiedTime, owners, permissions, webViewLink, iconLink, thumbnailLink, description)",
      supportsAllDrives: true,
      includeItemsFromAllDrives: true,
      pageSize: this.config.batchSize,
    };

    try {
      const response = await this.driveAPI.listFiles(userEmail, scanOptions);
      const files = response.files || [];

      // Log scanning progress
      console.log(
        `📁 Scanned folder ${folder.path || folder.id}: ${
          files.length
        } files found`
      );

      // If no files found in root, provide diagnostic info
      if (files.length === 0 && folder.id === "root") {
        console.log(`⚠️ No files found in root folder for ${userEmail}`);
        console.log(`💡 This could mean:`);
        console.log(`   1. User has no files in their Drive`);
        console.log(`   2. All files are in shared drives`);
        console.log(`   3. Domain-wide delegation is not configured correctly`);
        console.log(`   4. User is not in the organization`);
      }

      return files;
    } catch (error) {
      console.error(
        `❌ Error scanning folder ${folder.path || folder.id}:`,
        error.message
      );

      // Provide specific error guidance
      if (error.message.includes("unauthorized_client")) {
        console.log(
          `💡 Fix: Enable domain-wide delegation for service account in Google Admin Console`
        );
      } else if (error.message.includes("insufficient permission")) {
        console.log(
          `💡 Fix: Ensure user ${userEmail} is in the organization and has Drive access`
        );
      }

      throw error;
    }
  }

  /**
   * Scan and retrieve all users from Google Drive domain
   * @returns {Promise<Array>} List of users
   * @throws {Error} If scanning fails
   */
  async scanAllUsers() {
    try {
      const allUsers = await this.driveAPI.getAllUsers();

      console.log(`📁 Found ${allUsers.length} users in domain`);

      if (allUsers.length === 0) {
        console.warn("⚠️ No users found in the domain");
        console.log("💡 Possible reasons:");
        console.log("   1. No users exist in the domain");
        console.log("   2. Domain-wide delegation is not configured correctly");
        console.log("   3. Service account lacks necessary permissions");
        console.log("   4. API access is restricted");
        return [];
      }

      return allUsers;
    } catch (error) {
      console.error("❌ Error scanning all users:", error.message);

      // Provide specific error guidance
      if (error.message.includes("unauthorized_client")) {
        console.log(
          "💡 Fix: Enable domain-wide delegation for service account in Google Admin Console"
        );
      } else if (error.message.includes("insufficient permission")) {
        console.log(
          "💡 Fix: Ensure the service account has the correct scopes and permissions"
        );
      }

      throw new Error(`Failed to scan all users: ${error.message}`);
    }
  }

  /**
   * Build full file path
   * @param {string} parentPath - Parent folder path
   * @param {string} fileName - File name
   * @returns {string} Full path
   */
  buildFilePath(parentPath, fileName) {
    if (parentPath === "/") {
      return `/${fileName}`;
    }
    return `${parentPath}/${fileName}`;
  }

  /**
   * Create scan session in database
   * @param {string} userEmail - User email
   * @param {object} options - Scan options
   * @returns {Promise<object>} Scan session
   */
  async createScanSession(userEmail, options) {
    const { data, error } = await this.supabase
      .getServiceClient()
      .from("scan_sessions")
      .insert({
        user_email: userEmail,
        scan_type: "full_drive",
        scan_options: options,
        status: "running",
        started_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create scan session: ${error.message}`);
    }

    return data;
  }

  /**
   * Update scan session
   * @param {string} sessionId - Session ID
   * @param {object} updates - Updates to apply
   */
  async updateScanSession(sessionId, updates) {
    const { error } = await this.supabase
      .getServiceClient()
      .from("scan_sessions")
      .update(updates)
      .eq("id", sessionId);

    if (error) {
      console.error("❌ Error updating scan session:", error.message);
    }
  }

  /**
   * Update scan progress
   * @param {string} sessionId - Session ID
   * @param {object} progress - Progress data
   */
  async updateScanProgress(sessionId, progress) {
    const { error } = await this.supabase
      .getServiceClient()
      .from("scan_sessions")
      .update({
        ...progress,
        updated_at: new Date().toISOString(),
      })
      .eq("id", sessionId);

    if (error) {
      console.error("❌ Error updating scan progress:", error.message);
    }
  }

  /**
   * Store scanned file in database
   * @param {object} file - File data
   * @param {string} sessionId - Session ID
   */
  async storeScannedFile(file, sessionId) {
    const { error } = await this.supabase
      .getServiceClient()
      .from("scanned_files")
      .insert({
        scan_session_id: sessionId,
        file_id: file.id,
        name: file.name,
        mime_type: file.mimeType,
        size: parseInt(file.size) || 0,
        full_path: file.fullPath,
        depth: file.depth,
        parents: file.parents,
        created_time: file.createdTime,
        modified_time: file.modifiedTime,
        owners: file.owners,
        permissions: file.permissions,
        web_view_link: file.webViewLink,
        metadata: {
          iconLink: file.iconLink,
          thumbnailLink: file.thumbnailLink,
          description: file.description,
        },
        user_email: file.owners[0].emailAddress,
        domain: process.env.GOOGLE_DOMAIN,
      });

    if (error) {
      console.error("❌ Error storing scanned file:", error.message);
    }
  }

  /**
   * Get scan status
   * @returns {object} Current scan state
   */
  getScanStatus() {
    return {
      ...this.scanState,
      duration: this.scanState.startTime
        ? Date.now() - this.scanState.startTime
        : 0,
    };
  }

  /**
   * Stop current scan
   */
  stopScan() {
    this.scanState.isScanning = false;
    console.log("🛑 Drive scan stopped by user");
  }
}

// Export singleton instance
export const driveScanner = new DriveScanner();
