import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { supabaseClient } from './database/supabase.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Apply database schema to Supabase using direct table creation
 */
async function applyDatabaseSchemaFix() {
    console.log('🗄️ Applying Database Schema to Supabase (Fixed Version)...\n');

    try {
        const client = supabaseClient.getServiceClient();

        // Create tables one by one
        const tables = [
            {
                name: 'users',
                sql: `
                CREATE TABLE IF NOT EXISTS users (
                  id SERIAL PRIMARY KEY,
                  email_google TEXT UNIQUE NOT NULL,
                  lark_userid TEXT,
                  mapped BOOLEAN DEFAULT FALSE,
                  created_at TIMESTAMPTZ DEFAULT NOW(),
                  updated_at TIMESTAMPTZ DEFAULT NOW()
                );
                `
            },
            {
                name: 'migration_tasks',
                sql: `
                CREATE TABLE IF NOT EXISTS migration_tasks (
                  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                  task_name TEXT NOT NULL,
                  source_user_email TEXT NOT NULL,
                  target_lark_userid TEXT NOT NULL,
                  scope JSONB NOT NULL,
                  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
                  progress_files INTEGER DEFAULT 0,
                  progress_total INTEGER DEFAULT 0,
                  owner_email TEXT NOT NULL,
                  created_at TIMESTAMPTZ DEFAULT NOW(),
                  updated_at TIMESTAMPTZ DEFAULT NOW(),
                  started_at TIMESTAMPTZ,
                  completed_at TIMESTAMPTZ,
                  error_details TEXT
                );
                `
            },
            {
                name: 'scan_sessions',
                sql: `
                CREATE TABLE IF NOT EXISTS scan_sessions (
                  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                  user_email TEXT NOT NULL,
                  scan_type TEXT NOT NULL CHECK (scan_type IN ('full_drive', 'folder_specific', 'selective')),
                  scan_options JSONB,
                  status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
                  total_files INTEGER DEFAULT 0,
                  scanned_files INTEGER DEFAULT 0,
                  current_depth INTEGER DEFAULT 0,
                  folders_processed INTEGER DEFAULT 0,
                  total_size BIGINT DEFAULT 0,
                  scan_duration INTEGER,
                  started_at TIMESTAMPTZ DEFAULT NOW(),
                  completed_at TIMESTAMPTZ,
                  updated_at TIMESTAMPTZ DEFAULT NOW(),
                  error_message TEXT
                );
                `
            },
            {
                name: 'scanned_files',
                sql: `
                CREATE TABLE IF NOT EXISTS scanned_files (
                  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                  scan_session_id UUID NOT NULL,
                  file_id TEXT NOT NULL,
                  name TEXT NOT NULL,
                  mime_type TEXT NOT NULL,
                  size BIGINT DEFAULT 0,
                  full_path TEXT NOT NULL,
                  depth INTEGER NOT NULL DEFAULT 0,
                  parents TEXT[],
                  created_time TIMESTAMPTZ,
                  modified_time TIMESTAMPTZ,
                  owners JSONB,
                  permissions JSONB,
                  web_view_link TEXT,
                  metadata JSONB,
                  is_selected BOOLEAN DEFAULT FALSE,
                  created_at TIMESTAMPTZ DEFAULT NOW()
                );
                `
            },
            {
                name: 'migration_items',
                sql: `
                CREATE TABLE IF NOT EXISTS migration_items (
                  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                  migration_task_id UUID NOT NULL,
                  google_file_id TEXT NOT NULL,
                  google_file_name TEXT NOT NULL,
                  google_file_path TEXT NOT NULL,
                  google_file_type TEXT NOT NULL,
                  file_size BIGINT DEFAULT 0,
                  lark_file_id TEXT,
                  lark_file_token TEXT,
                  lark_file_url TEXT,
                  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'uploading', 'completed', 'failed', 'skipped')),
                  error_details TEXT,
                  created_at TIMESTAMPTZ DEFAULT NOW(),
                  updated_at TIMESTAMPTZ DEFAULT NOW()
                );
                `
            },
            {
                name: 'permission_mappings',
                sql: `
                CREATE TABLE IF NOT EXISTS permission_mappings (
                  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                  migration_item_id UUID NOT NULL,
                  google_email TEXT NOT NULL,
                  google_permission_type TEXT NOT NULL,
                  lark_userid TEXT,
                  lark_permission_type TEXT,
                  mapped BOOLEAN DEFAULT FALSE,
                  error_details TEXT,
                  created_at TIMESTAMPTZ DEFAULT NOW(),
                  updated_at TIMESTAMPTZ DEFAULT NOW()
                );
                `
            },
            {
                name: 'migration_logs',
                sql: `
                CREATE TABLE IF NOT EXISTS migration_logs (
                  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                  task_id UUID NOT NULL,
                  level TEXT NOT NULL CHECK (level IN ('info', 'warning', 'error', 'debug')),
                  message TEXT NOT NULL,
                  details JSONB,
                  created_at TIMESTAMPTZ DEFAULT NOW()
                );
                `
            }
        ];

        // Create each table
        for (const table of tables) {
            console.log(`📋 Creating table: ${table.name}`);
            try {
                const { error } = await client.from('').select().sql(table.sql);

                if (error && !error.message.includes('already exists')) {
                    console.log(`   ❌ Error: ${error.message}`);
                } else {
                    console.log(`   ✅ Success`);
                }
            } catch (error) {
                console.log(`   ❌ Exception: ${error.message}`);
            }
        }

        // Add foreign key constraints
        console.log('\n📎 Adding foreign key constraints...');
        const constraints = [
            'ALTER TABLE scanned_files ADD CONSTRAINT fk_scanned_files_session FOREIGN KEY (scan_session_id) REFERENCES scan_sessions(id) ON DELETE CASCADE;',
            'ALTER TABLE migration_items ADD CONSTRAINT fk_migration_items_task FOREIGN KEY (migration_task_id) REFERENCES migration_tasks(id) ON DELETE CASCADE;',
            'ALTER TABLE permission_mappings ADD CONSTRAINT fk_permission_mappings_item FOREIGN KEY (migration_item_id) REFERENCES migration_items(id) ON DELETE CASCADE;',
            'ALTER TABLE migration_logs ADD CONSTRAINT fk_migration_logs_task FOREIGN KEY (task_id) REFERENCES migration_tasks(id) ON DELETE CASCADE;'
        ];

        for (const constraint of constraints) {
            try {
                const { error } = await client.from('').select().sql(constraint);
                if (error && !error.message.includes('already exists')) {
                    console.log(`   ❌ Error: ${error.message}`);
                } else {
                    console.log(`   ✅ Constraint added`);
                }
            } catch (error) {
                console.log(`   ⚠️ Constraint warning: ${error.message}`);
            }
        }

        console.log('\n✅ Database schema application completed!');

        // Test the schema
        await testDatabaseSchema();

    } catch (error) {
        console.error('❌ Failed to apply database schema:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

/**
 * Test the database schema
 */
async function testDatabaseSchema() {
    console.log('\n🧪 Testing database schema...');

    const client = supabaseClient.getServiceClient();

    const tables = ['users', 'migration_tasks', 'scan_sessions', 'scanned_files', 'migration_items', 'permission_mappings', 'migration_logs'];

    for (const table of tables) {
        try {
            const { data, error } = await client.from(table).select('*').limit(1);

            if (error) {
                console.log(`❌ Table ${table}: ${error.message}`);
            } else {
                console.log(`✅ Table ${table}: OK`);
            }
        } catch (error) {
            console.log(`❌ Table ${table}: ${error.message}`);
        }
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    console.log('🚀 Database Schema Fix Tool');
    applyDatabaseSchemaFix();
}

export { applyDatabaseSchemaFix };
