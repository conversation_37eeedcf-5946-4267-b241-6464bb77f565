import { supabaseClient } from './database/supabase.js';

/**
 * Manually create the essential tables for the scanning feature
 */
async function createEssentialTables() {
    console.log('🗄️ Creating essential database tables...\n');

    const client = supabaseClient.getServiceClient();

    try {
        // Create scan_sessions table
        console.log('📋 Creating scan_sessions table...');
        const { data: sessions, error: sessionsError } = await client
            .from('scan_sessions')
            .select('*')
            .limit(1);

        if (sessionsError && sessionsError.message.includes('does not exist')) {
            // Table doesn't exist, let's create it manually
            console.log('   ❌ Table does not exist. Please create it manually in Supabase dashboard.');
            console.log('   📋 SQL to run in Supabase SQL editor:');
            console.log(`
CREATE TABLE scan_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_email TEXT NOT NULL,
  scan_type TEXT NOT NULL CHECK (scan_type IN ('full_drive', 'folder_specific', 'selective')),
  scan_options JSONB,
  status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  scanned_files INTEGER DEFAULT 0,
  current_depth INTEGER DEFAULT 0,
  folders_processed INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0,
  scan_duration INTEGER,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT
);

CREATE INDEX idx_scan_sessions_status ON scan_sessions(status);
CREATE INDEX idx_scan_sessions_started_at ON scan_sessions(started_at);

-- Enable RLS
ALTER TABLE scan_sessions ENABLE ROW LEVEL SECURITY;

-- Add policy for service role
CREATE POLICY "Service role full access scan_sessions" ON scan_sessions
  FOR ALL USING (auth.role() = 'service_role');
            `);
        } else {
            console.log('   ✅ scan_sessions table exists');
        }

        // Create scanned_files table
        console.log('\n📋 Creating scanned_files table...');
        const { data: files, error: filesError } = await client
            .from('scanned_files')
            .select('*')
            .limit(1);

        if (filesError && filesError.message.includes('does not exist')) {
            console.log('   ❌ Table does not exist. Please create it manually in Supabase dashboard.');
            console.log('   📋 SQL to run in Supabase SQL editor:');
            console.log(`
CREATE TABLE scanned_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scan_session_id UUID NOT NULL REFERENCES scan_sessions(id) ON DELETE CASCADE,
  file_id TEXT NOT NULL,
  name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  size BIGINT DEFAULT 0,
  full_path TEXT NOT NULL,
  depth INTEGER NOT NULL DEFAULT 0,
  parents TEXT[],
  created_time TIMESTAMPTZ,
  modified_time TIMESTAMPTZ,
  owners JSONB,
  permissions JSONB,
  web_view_link TEXT,
  metadata JSONB,
  is_selected BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_scanned_files_file_id ON scanned_files(file_id);
CREATE INDEX idx_scanned_files_mime_type ON scanned_files(mime_type);
CREATE INDEX idx_scanned_files_full_path ON scanned_files(full_path);
CREATE INDEX idx_scanned_files_is_selected ON scanned_files(is_selected);
CREATE INDEX idx_scanned_files_depth ON scanned_files(depth);

-- Enable RLS
ALTER TABLE scanned_files ENABLE ROW LEVEL SECURITY;

-- Add policy for service role
CREATE POLICY "Service role full access scanned_files" ON scanned_files
  FOR ALL USING (auth.role() = 'service_role');
            `);
        } else {
            console.log('   ✅ scanned_files table exists');
        }

        console.log('\n✅ Essential tables check completed!');
        console.log('\n💡 If tables don\'t exist, please:');
        console.log('1. Go to your Supabase dashboard');
        console.log('2. Navigate to SQL Editor');
        console.log('3. Copy and paste the SQL statements shown above');
        console.log('4. Execute them to create the tables');

    } catch (error) {
        console.error('❌ Error checking tables:', error.message);
    }
}

// Run the function
createEssentialTables();
