import express from "express";
import { driveScanner } from "../services/drive-scanner.js";
import { supabaseClient } from "../database/supabase.js";

const router = express.Router();

/**
 * Start a new Drive scan
 * POST /api/scan/start
 */
router.post("/start", async (req, res) => {
  try {
    const { userEmail, scope, ...options } = req.body;

    if (!userEmail) {
      return res.status(400).json({
        error: "User email is required",
      });
    }

    console.log(`🔍 Starting scan for ${userEmail} with scope: ${scope}`);

    // Start the scan
    const result = await driveScanner.startFullScan(userEmail, {
      scope,
      ...options,
    });

    res.json({
      success: true,
      sessionId: result.sessionId,
      message: "<PERSON>an started successfully",
    });
  } catch (error) {
    console.error("❌ Error starting scan:", error.message);
    res.status(500).json({
      error: "Failed to start scan",
      details: error.message,
    });
  }
});

/**
 * Get scan status
 * GET /api/scan/status/:sessionId
 */
router.get("/status/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;

    const { data: session, error } = await supabaseClient
      .getServiceClient()
      .from("scan_sessions")
      .select("*")
      .eq("id", sessionId)
      .single();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!session) {
      return res.status(404).json({
        error: "Scan session not found",
      });
    }

    res.json(session);
  } catch (error) {
    console.error("❌ Error getting scan status:", error.message);
    res.status(500).json({
      error: "Failed to get scan status",
      details: error.message,
    });
  }
});

/**
 * Cancel a running scan
 * POST /api/scan/cancel/:sessionId
 */
router.post("/cancel/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;

    // Stop the scanner
    driveScanner.stopScan();

    // Update session status in database
    const { error } = await supabaseClient
      .getServiceClient()
      .from("scan_sessions")
      .update({
        status: "cancelled",
        completed_at: new Date().toISOString(),
      })
      .eq("id", sessionId);

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    res.json({
      success: true,
      message: "Scan cancelled successfully",
    });
  } catch (error) {
    console.error("❌ Error cancelling scan:", error.message);
    res.status(500).json({
      error: "Failed to cancel scan",
      details: error.message,
    });
  }
});

/**
 * Get scanned files with pagination and filtering
 * GET /api/scan/files
 */
router.get("/files", async (req, res) => {
  try {
    const {
      sessionId,
      page = 1,
      pageSize = 50,
      search = "",
      mimeType = "all",
      minSize = "",
      maxSize = "",
      sortBy = "name",
      sortOrder = "asc",
    } = req.query;

    if (!sessionId) {
      return res.status(400).json({
        error: "Session ID is required",
      });
    }

    // Build query
    let query = supabaseClient
      .getServiceClient()
      .from("scanned_files")
      .select("*", { count: "exact" })
      .eq("scan_session_id", sessionId);

    // Apply filters
    if (search) {
      query = query.ilike("name", `%${search}%`);
    }

    if (mimeType !== "all") {
      if (mimeType.endsWith("/")) {
        // Prefix match for categories like 'image/'
        query = query.like("mime_type", `${mimeType}%`);
      } else {
        // Exact match
        query = query.eq("mime_type", mimeType);
      }
    }

    if (minSize) {
      query = query.gte("size", parseInt(minSize));
    }

    if (maxSize) {
      query = query.lte("size", parseInt(maxSize));
    }

    // Apply sorting
    const ascending = sortOrder === "asc";
    query = query.order(sortBy, { ascending });

    // Apply pagination
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    query = query.range(offset, offset + parseInt(pageSize) - 1);

    const { data: files, error, count } = await query;

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    res.json({
      files: files || [],
      totalCount: count || 0,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      totalPages: Math.ceil((count || 0) / parseInt(pageSize)),
    });
  } catch (error) {
    console.error("❌ Error getting scanned files:", error.message);
    res.status(500).json({
      error: "Failed to get scanned files",
      details: error.message,
    });
  }
});

/**
 * Get scanned files in tree structure
 * GET /api/scan/files/tree
 */
router.get("/files/tree", async (req, res) => {
  try {
    const { sessionId, search = "", mimeType = "all" } = req.query;
    console.log(
      "🌳 Tree API called with sessionId:",
      sessionId,
      "search:",
      search,
      "mimeType:",
      mimeType
    );

    if (!sessionId) {
      return res.status(400).json({
        error: "Session ID is required",
      });
    }

    // Build query to get all files
    let query = supabaseClient
      .getServiceClient()
      .from("scanned_files")
      .select("*")
      .eq("scan_session_id", sessionId);

    // Apply filters
    if (search) {
      query = query.ilike("name", `%${search}%`);
    }

    if (mimeType !== "all") {
      if (mimeType.endsWith("/")) {
        query = query.like("mime_type", `${mimeType}%`);
      } else {
        query = query.eq("mime_type", mimeType);
      }
    }

    // Sort by full_path to maintain hierarchy
    query = query.order("full_path", { ascending: true });

    const { data: files, error } = await query;

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    // Build tree structure
    const tree = buildFileTree(files || []);

    // Calculate statistics
    const stats = calculateTreeStats(files || []);

    res.json({
      tree,
      stats,
      totalFiles: files?.length || 0,
    });
  } catch (error) {
    console.error("❌ Error getting scanned files tree:", error.message);
    res.status(500).json({
      error: "Failed to get scanned files tree",
      details: error.message,
    });
  }
});

/**
 * Build tree structure from flat file list
 */
function buildFileTree(files) {
  const tree = [];
  const pathMap = new Map();

  // Sort files by path depth and name (alphabetically)
  files.sort((a, b) => {
    const depthDiff = a.depth - b.depth;
    if (depthDiff !== 0) return depthDiff;

    // Sort alphabetically by full path, case-insensitive
    return a.full_path.toLowerCase().localeCompare(b.full_path.toLowerCase());
  });

  files.forEach((file) => {
    const pathParts = file.full_path
      .split("/")
      .filter((part) => part.length > 0);
    let currentLevel = tree;
    let currentPath = "";

    // Navigate/create path structure
    for (let i = 0; i < pathParts.length - 1; i++) {
      currentPath += "/" + pathParts[i];

      let folder = currentLevel.find(
        (item) => item.type === "folder" && item.name === pathParts[i]
      );

      if (!folder) {
        folder = {
          id: `folder_${currentPath}`,
          name: pathParts[i],
          type: "folder",
          path: currentPath,
          children: [],
          fileCount: 0,
          folderCount: 0,
          totalSize: 0,
        };
        currentLevel.push(folder);
        pathMap.set(currentPath, folder);
      }

      currentLevel = folder.children;
    }

    // Add the file
    const fileNode = {
      ...file,
      type:
        file.mime_type === "application/vnd.google-apps.folder"
          ? "folder"
          : "file",
      children:
        file.mime_type === "application/vnd.google-apps.folder"
          ? []
          : undefined,
    };

    currentLevel.push(fileNode);

    // Update parent folder statistics
    let parentPath = "";
    for (let i = 0; i < pathParts.length - 1; i++) {
      parentPath += "/" + pathParts[i];
      const parentFolder = pathMap.get(parentPath);
      if (parentFolder) {
        if (fileNode.type === "folder") {
          parentFolder.folderCount++;
        } else {
          parentFolder.fileCount++;
          parentFolder.totalSize += file.size || 0;
        }
      }
    }
  });

  return tree;
}

/**
 * Calculate tree statistics
 */
function calculateTreeStats(files) {
  const stats = {
    totalFiles: 0,
    totalFolders: 0,
    totalSize: 0,
    fileTypes: {},
  };

  files.forEach((file) => {
    if (file.mime_type === "application/vnd.google-apps.folder") {
      stats.totalFolders++;
    } else {
      stats.totalFiles++;
      stats.totalSize += file.size || 0;

      // Count file types
      const mimeType = file.mime_type || "unknown";
      stats.fileTypes[mimeType] = (stats.fileTypes[mimeType] || 0) + 1;
    }
  });

  return stats;
}

/**
 * Update file selection status
 * POST /api/scan/files/select
 */
router.post("/files/select", async (req, res) => {
  try {
    const { fileIds, isSelected } = req.body;

    if (!Array.isArray(fileIds)) {
      return res.status(400).json({
        error: "fileIds must be an array",
      });
    }

    const { error } = await supabaseClient
      .getServiceClient()
      .from("scanned_files")
      .update({ is_selected: isSelected })
      .in("file_id", fileIds);

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    res.json({
      success: true,
      message: `Updated selection for ${fileIds.length} files`,
    });
  } catch (error) {
    console.error("❌ Error updating file selection:", error.message);
    res.status(500).json({
      error: "Failed to update file selection",
      details: error.message,
    });
  }
});

/**
 * Get scan statistics
 * GET /api/scan/stats/:sessionId
 */
router.get("/stats/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;

    // Get session info
    const { data: session, error: sessionError } = await supabaseClient
      .getServiceClient()
      .from("scan_sessions")
      .select("*")
      .eq("id", sessionId)
      .single();

    if (sessionError) {
      throw new Error(`Database error: ${sessionError.message}`);
    }

    // Get file statistics
    const { data: fileStats, error: statsError } = await supabaseClient
      .getServiceClient()
      .from("scanned_files")
      .select("mime_type, size")
      .eq("scan_session_id", sessionId);

    if (statsError) {
      throw new Error(`Database error: ${statsError.message}`);
    }

    // Calculate statistics
    const stats = {
      totalFiles: fileStats.length,
      totalSize: fileStats.reduce((sum, file) => sum + (file.size || 0), 0),
      fileTypes: {},
      selectedFiles: 0,
      selectedSize: 0,
    };

    // Group by file type
    fileStats.forEach((file) => {
      const mimeType = file.mime_type;
      if (!stats.fileTypes[mimeType]) {
        stats.fileTypes[mimeType] = { count: 0, size: 0 };
      }
      stats.fileTypes[mimeType].count++;
      stats.fileTypes[mimeType].size += file.size || 0;
    });

    // Get selected files count
    const { count: selectedCount, error: selectedError } = await supabaseClient
      .getServiceClient()
      .from("scanned_files")
      .select("*", { count: "exact", head: true })
      .eq("scan_session_id", sessionId)
      .eq("is_selected", true);

    if (!selectedError) {
      stats.selectedFiles = selectedCount || 0;
    }

    res.json({
      session,
      stats,
    });
  } catch (error) {
    console.error("❌ Error getting scan statistics:", error.message);
    res.status(500).json({
      error: "Failed to get scan statistics",
      details: error.message,
    });
  }
});

/**
 * Get all users
 * GET /api/scan/users
 */
router.get("/users", async (req, res) => {
  try {
    const forceScan = req.query.forceScan === "true";

    // Always fetch from DB first
    let { data: users, error } = await supabaseClient
      .getServiceClient()
      .from("scanned_users")
      .select("*");

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    // If no users in DB or forceScan requested, scan and upsert
    if (!users || users.length === 0 || forceScan) {
      const allUsers = await driveScanner.scanAllUsers();
      if (allUsers.length > 0) {
        const upsertUsers = allUsers.map((user) => ({
          user_id: user.id,
          primary_email: user.primaryEmail,
          given_name: user.name.givenName,
          family_name: user.name.familyName,
          full_name: user.name.fullName,
          last_login_time: user.lastLoginTime,
          suspended: user.suspended,
        }));

        const { error: upsertError } = await supabaseClient
          .getServiceClient()
          .from("scanned_users")
          .upsert(upsertUsers, { onConflict: ["user_id"] });

        if (upsertError) {
          throw new Error(`Upsert error: ${upsertError.message}`);
        }

        // Fetch again after upsert to ensure latest data
        const { data: refreshedUsers, error: refreshError } =
          await supabaseClient
            .getServiceClient()
            .from("scanned_users")
            .select("*");

        if (refreshError) {
          throw new Error(`Database error: ${refreshError.message}`);
        }
        users = refreshedUsers;
      }
    }

    res.json({
      users: users.map((user) => ({
        id: user.id,
        userId: user.user_id,
        email: user.primary_email,
        givenName: user.given_name,
        familyName: user.family_name,
        fullName: user.full_name,
        lastLoginTime: user.last_login_time,
        suspended: user.suspended,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
      })),
      totalCount: users ? users.length : 0,
    });
  } catch (error) {
    console.error("❌ Error getting users:", error.message);
    res.status(500).json({
      error: "Failed to get users",
      details: error.message,
    });
  }
});

export default router;
