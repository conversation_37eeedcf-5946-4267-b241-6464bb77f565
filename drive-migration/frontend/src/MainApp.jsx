import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Navigation from './components/Navigation';
import Migration from './pages/Migration';
import Download from './pages/Download';
import UsersOverview from './pages/UsersOverview';
import { ToastProvider } from './contexts/ToastContext';
import './MainApp.css';

function MainApp() {
    return (
        <ToastProvider>
            <Router>
                <div className="main-app">
                    <Navigation />
                    <div className="main-content">
                        <Routes>
                            <Route path="/" element={<Navigate to="/migration" replace />} />
                            <Route path="/migration" element={<Migration />} />
                            <Route path="/download" element={<Download />} />
                            <Route path="/users" element={<UsersOverview />} />
                        </Routes>
                    </div>
                </div>
            </Router>
        </ToastProvider>
    );
}

export default MainApp;
