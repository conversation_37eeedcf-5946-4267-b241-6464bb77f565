.download-config-form {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
}

.download-config-form h2 {
    color: #2c3e50;
    margin-bottom: 2rem;
    text-align: center;
    border-bottom: 2px solid #3498db;
    padding-bottom: 1rem;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input[type="text"],
.form-group input[type="number"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group input:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.help-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.error-message {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* User Selection */
.user-selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
}

.search-box {
    flex: 1;
}

.search-box input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.select-all label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #3498db;
    cursor: pointer;
}

.select-all input[type="checkbox"] {
    margin: 0;
}

/* Users List */
.users-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #f8f9fa;
}

.no-users {
    padding: 2rem;
    text-align: center;
    color: #6c757d;
}

.user-item {
    border-bottom: 1px solid #e9ecef;
}

.user-item:last-child {
    border-bottom: none;
}

.user-checkbox {
    display: flex;
    align-items: center;
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
    gap: 1rem;
}

.user-checkbox:hover {
    background-color: #e3f2fd;
}

.user-checkbox input[type="checkbox"] {
    margin: 0;
    flex-shrink: 0;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.user-email {
    font-size: 0.875rem;
    color: #6c757d;
}

.user-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    flex-shrink: 0;
}

.file-count {
    font-size: 0.875rem;
    color: #3498db;
    font-weight: 500;
}

.file-size {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Form Actions */
.form-actions {
    margin-top: 2rem;
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .download-config-form {
        padding: 1rem;
        margin: 0 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .user-selection-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .user-checkbox {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .user-stats {
        align-items: flex-start;
        flex-direction: row;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .download-config-form {
        padding: 0.75rem;
    }
    
    .download-config-form h2 {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .users-list {
        max-height: 300px;
    }
    
    .user-checkbox {
        padding: 0.75rem;
    }
    
    .btn {
        width: 100%;
        padding: 1rem;
    }
}

/* Custom Scrollbar for Users List */
.users-list::-webkit-scrollbar {
    width: 6px;
}

.users-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.users-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.users-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
