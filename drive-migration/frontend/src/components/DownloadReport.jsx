import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import './DownloadReport.css';

const DownloadReport = ({ session, onStartNew }) => {
    const [reportData, setReportData] = useState(null);
    const [failedItems, setFailedItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showFailedItems, setShowFailedItems] = useState(false);

    useEffect(() => {
        if (session?.id) {
            loadReportData();
            loadFailedItems();
        }
    }, [session?.id]);

    /**
     * Load report data
     */
    const loadReportData = async () => {
        try {
            const response = await apiGet(`/api/download/sessions/${session.id}/progress`);
            if (response.success) {
                setReportData(response.data);
            }
        } catch (error) {
            console.error('Error loading report data:', error);
        } finally {
            setLoading(false);
        }
    };

    /**
     * Load failed items
     */
    const loadFailedItems = async () => {
        try {
            const response = await apiGet(`/api/download/sessions/${session.id}/items?status=failed&limit=100`);
            if (response.success) {
                setFailedItems(response.data.items);
            }
        } catch (error) {
            console.error('Error loading failed items:', error);
        }
    };

    /**
     * Export report as JSON
     */
    const exportReport = () => {
        const exportData = {
            session: reportData.session,
            progress: reportData.progress,
            failedItems: failedItems,
            exportedAt: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `download-report-${session.id}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    };

    /**
     * Export failed items as CSV
     */
    const exportFailedItems = () => {
        if (failedItems.length === 0) {
            alert('No failed items to export');
            return;
        }

        const headers = ['File Name', 'File Path', 'User Email', 'Error Message', 'Retry Count'];
        const csvContent = [
            headers.join(','),
            ...failedItems.map(item => [
                `"${item.file_name}"`,
                `"${item.file_path}"`,
                `"${item.user_email}"`,
                `"${item.error_message || ''}"`,
                item.retry_count || 0
            ].join(','))
        ].join('\n');

        const dataBlob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `failed-downloads-${session.id}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    };

    /**
     * Format file size
     */
    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    /**
     * Format duration
     */
    const formatDuration = (seconds) => {
        if (!seconds) return '-';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    };

    /**
     * Get success rate
     */
    const getSuccessRate = () => {
        if (!reportData?.session?.total_files) return 0;
        const completed = reportData.progress.completed || 0;
        const total = reportData.session.total_files;
        return ((completed / total) * 100).toFixed(1);
    };

    /**
     * Get status color
     */
    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return '#27ae60';
            case 'failed': return '#e74c3c';
            case 'cancelled': return '#95a5a6';
            default: return '#6c757d';
        }
    };

    if (loading) {
        return (
            <div className="download-report loading">
                <div className="loading-spinner"></div>
                <p>Loading report...</p>
            </div>
        );
    }

    if (!reportData) {
        return (
            <div className="download-report error">
                <p>Failed to load report data</p>
            </div>
        );
    }

    const { session: sessionData, progress } = reportData;
    const isSuccess = sessionData.status === 'completed';
    const hasFailures = (progress.failed || 0) > 0;

    return (
        <div className="download-report">
            <div className="report-header">
                <h2>Download Report</h2>
                <div className="session-status" style={{ color: getStatusColor(sessionData.status) }}>
                    {sessionData.status === 'completed' ? '✅' : sessionData.status === 'failed' ? '❌' : '⚠️'} 
                    {sessionData.status.toUpperCase()}
                </div>
            </div>

            {/* Summary Cards */}
            <div className="summary-cards">
                <div className="summary-card success">
                    <div className="card-icon">✅</div>
                    <div className="card-content">
                        <div className="card-value">{progress.completed || 0}</div>
                        <div className="card-label">Files Downloaded</div>
                    </div>
                </div>

                <div className="summary-card total">
                    <div className="card-icon">📁</div>
                    <div className="card-content">
                        <div className="card-value">{sessionData.total_files || 0}</div>
                        <div className="card-label">Total Files</div>
                    </div>
                </div>

                <div className="summary-card size">
                    <div className="card-icon">💾</div>
                    <div className="card-content">
                        <div className="card-value">{formatFileSize(sessionData.downloaded_size || 0)}</div>
                        <div className="card-label">Downloaded Size</div>
                    </div>
                </div>

                <div className="summary-card rate">
                    <div className="card-icon">📊</div>
                    <div className="card-content">
                        <div className="card-value">{getSuccessRate()}%</div>
                        <div className="card-label">Success Rate</div>
                    </div>
                </div>
            </div>

            {/* Session Details */}
            <div className="session-details">
                <h3>Session Details</h3>
                <div className="details-grid">
                    <div className="detail-item">
                        <span className="detail-label">Session Name:</span>
                        <span className="detail-value">{sessionData.name}</span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Started:</span>
                        <span className="detail-value">
                            {sessionData.started_at ? new Date(sessionData.started_at).toLocaleString() : '-'}
                        </span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Completed:</span>
                        <span className="detail-value">
                            {sessionData.completed_at ? new Date(sessionData.completed_at).toLocaleString() : '-'}
                        </span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Duration:</span>
                        <span className="detail-value">{formatDuration(sessionData.duration_seconds)}</span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Download Path:</span>
                        <span className="detail-value">{sessionData.download_path}</span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Concurrent Downloads:</span>
                        <span className="detail-value">{sessionData.concurrent_downloads}</span>
                    </div>
                </div>
            </div>

            {/* Status Breakdown */}
            <div className="status-breakdown">
                <h3>Status Breakdown</h3>
                <div className="status-grid">
                    <div className="status-item completed">
                        <div className="status-count">{progress.completed || 0}</div>
                        <div className="status-label">Completed</div>
                    </div>
                    <div className="status-item failed">
                        <div className="status-count">{progress.failed || 0}</div>
                        <div className="status-label">Failed</div>
                    </div>
                    <div className="status-item skipped">
                        <div className="status-count">{progress.skipped || 0}</div>
                        <div className="status-label">Skipped</div>
                    </div>
                    <div className="status-item pending">
                        <div className="status-count">{progress.pending || 0}</div>
                        <div className="status-label">Pending</div>
                    </div>
                </div>
            </div>

            {/* Failed Items */}
            {hasFailures && (
                <div className="failed-items-section">
                    <div className="section-header">
                        <h3>Failed Downloads ({failedItems.length})</h3>
                        <button 
                            className="btn btn-secondary"
                            onClick={() => setShowFailedItems(!showFailedItems)}
                        >
                            {showFailedItems ? 'Hide' : 'Show'} Failed Items
                        </button>
                    </div>

                    {showFailedItems && (
                        <div className="failed-items-list">
                            {failedItems.map((item) => (
                                <div key={item.id} className="failed-item">
                                    <div className="item-info">
                                        <div className="item-name">{item.file_name}</div>
                                        <div className="item-path">{item.file_path}</div>
                                        <div className="item-user">{item.user_email}</div>
                                    </div>
                                    <div className="item-error">
                                        <div className="error-message">{item.error_message}</div>
                                        <div className="retry-count">Retries: {item.retry_count || 0}</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}

            {/* Actions */}
            <div className="report-actions">
                <button className="btn btn-primary" onClick={onStartNew}>
                    🆕 Start New Download
                </button>
                <button className="btn btn-secondary" onClick={exportReport}>
                    📄 Export Report
                </button>
                {hasFailures && (
                    <button className="btn btn-warning" onClick={exportFailedItems}>
                        📋 Export Failed Items
                    </button>
                )}
            </div>

            {/* Success/Failure Message */}
            <div className={`result-message ${isSuccess ? 'success' : 'failure'}`}>
                {isSuccess ? (
                    <div>
                        <h4>🎉 Download Completed Successfully!</h4>
                        <p>All files have been downloaded to: <strong>{sessionData.download_path}</strong></p>
                    </div>
                ) : (
                    <div>
                        <h4>⚠️ Download Completed with Issues</h4>
                        <p>
                            {progress.completed || 0} files downloaded successfully, 
                            {progress.failed || 0} files failed. 
                            Check the failed items above for details.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default DownloadReport;
