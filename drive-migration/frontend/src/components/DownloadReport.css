.download-report {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.download-report.loading,
.download-report.error {
    text-align: center;
    padding: 3rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Report Header */
.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 1rem;
}

.report-header h2 {
    margin: 0;
    color: #2c3e50;
}

.session-status {
    font-size: 1.25rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid #e9ecef;
    transition: transform 0.2s;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.summary-card.success {
    border-left: 4px solid #27ae60;
}

.summary-card.total {
    border-left: 4px solid #3498db;
}

.summary-card.size {
    border-left: 4px solid #9b59b6;
}

.summary-card.rate {
    border-left: 4px solid #f39c12;
}

.card-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.card-content {
    flex: 1;
}

.card-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.card-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 500;
}

/* Session Details */
.session-details {
    margin-bottom: 2rem;
}

.session-details h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.detail-label {
    font-weight: 600;
    color: #6c757d;
}

.detail-value {
    color: #2c3e50;
    font-weight: 500;
    text-align: right;
    word-break: break-word;
}

/* Status Breakdown */
.status-breakdown {
    margin-bottom: 2rem;
}

.status-breakdown h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.status-item {
    text-align: center;
    padding: 1.5rem;
    border-radius: 8px;
    border: 2px solid;
}

.status-item.completed {
    background: #d5f4e6;
    border-color: #27ae60;
}

.status-item.failed {
    background: #fadbd8;
    border-color: #e74c3c;
}

.status-item.skipped {
    background: #eaeded;
    border-color: #95a5a6;
}

.status-item.pending {
    background: #fef9e7;
    border-color: #f39c12;
}

.status-count {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.status-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
}

/* Failed Items Section */
.failed-items-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h3 {
    margin: 0;
    color: #e74c3c;
}

.failed-items-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #fff5f5;
}

.failed-item {
    display: flex;
    padding: 1rem;
    border-bottom: 1px solid #fadbd8;
    gap: 1rem;
}

.failed-item:last-child {
    border-bottom: none;
}

.failed-item:hover {
    background: #fef2f2;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.item-path {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.item-user {
    font-size: 0.8rem;
    color: #3498db;
}

.item-error {
    flex: 1;
    min-width: 0;
}

.error-message {
    font-size: 0.875rem;
    color: #e74c3c;
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.retry-count {
    font-size: 0.8rem;
    color: #f39c12;
    font-weight: 500;
}

/* Report Actions */
.report-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

/* Result Message */
.result-message {
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    border: 2px solid;
}

.result-message.success {
    background: #d5f4e6;
    border-color: #27ae60;
    color: #1e8449;
}

.result-message.failure {
    background: #fadbd8;
    border-color: #e74c3c;
    color: #c0392b;
}

.result-message h4 {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
}

.result-message p {
    margin: 0;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .download-report {
        padding: 1rem;
    }
    
    .report-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .failed-item {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .report-actions {
        flex-direction: column;
    }
    
    .detail-item {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .detail-value {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-card {
        flex-direction: column;
        text-align: center;
    }
    
    .card-icon {
        font-size: 1.5rem;
    }
    
    .card-value {
        font-size: 1.5rem;
    }
}
